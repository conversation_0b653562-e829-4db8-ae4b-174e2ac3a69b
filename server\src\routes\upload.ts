import express from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { v4 as uuidv4 } from 'uuid';
import { UploadedFile, ApiResponse, UploadResponse } from '../../../shared/types';
import { asyncHandler, createError } from '../middleware/errorHandler';

const router = express.Router();

// 确保上传目录存在
const uploadDir = path.join(__dirname, '../../../uploads');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// 配置multer存储
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const subDir = file.fieldname === 'video' ? 'videos' : 'subtitles';
    const targetDir = path.join(uploadDir, subDir);
    
    if (!fs.existsSync(targetDir)) {
      fs.mkdirSync(targetDir, { recursive: true });
    }
    
    cb(null, targetDir);
  },
  filename: (req, file, cb) => {
    const uniqueName = `${uuidv4()}-${Date.now()}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  }
});

// 文件过滤器
const fileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  if (file.fieldname === 'video') {
    // 视频文件验证
    const allowedVideoTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/mkv', 'video/webm'];
    if (allowedVideoTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('不支持的视频格式'));
    }
  } else if (file.fieldname === 'subtitles') {
    // 字幕文件验证
    const allowedSubtitleExts = ['.srt', '.vtt', '.ass', '.ssa'];
    const ext = path.extname(file.originalname).toLowerCase();
    if (allowedSubtitleExts.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error('不支持的字幕格式'));
    }
  } else {
    cb(new Error('未知的文件字段'));
  }
};

// 配置multer
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 500 * 1024 * 1024, // 500MB限制
    files: 10 // 最多10个文件
  }
});

/**
 * 上传文件
 */
router.post('/', upload.fields([
  { name: 'video', maxCount: 1 },
  { name: 'subtitles', maxCount: 5 }
]), asyncHandler(async (req, res) => {
  const files = req.files as { [fieldname: string]: Express.Multer.File[] };
  
  if (!files || (!files.video && !files.subtitles)) {
    throw createError('请选择要上传的文件', 400);
  }

  const response: UploadResponse = {
    subtitleFiles: []
  };

  // 处理视频文件
  if (files.video && files.video[0]) {
    const videoFile = files.video[0];
    response.videoFile = {
      id: uuidv4(),
      originalName: videoFile.originalname,
      filename: videoFile.filename,
      path: videoFile.path,
      size: videoFile.size,
      mimetype: videoFile.mimetype,
      uploadedAt: new Date()
    };
  }

  // 处理字幕文件
  if (files.subtitles) {
    response.subtitleFiles = files.subtitles.map(file => ({
      id: uuidv4(),
      originalName: file.originalname,
      filename: file.filename,
      path: file.path,
      size: file.size,
      mimetype: file.mimetype,
      uploadedAt: new Date()
    }));
  }

  const apiResponse: ApiResponse<UploadResponse> = {
    success: true,
    data: response,
    message: '文件上传成功'
  };

  res.json(apiResponse);
}));

/**
 * 获取上传的文件列表
 */
router.get('/list', asyncHandler(async (req, res) => {
  const videosDir = path.join(uploadDir, 'videos');
  const subtitlesDir = path.join(uploadDir, 'subtitles');
  
  const files: UploadedFile[] = [];
  
  // 读取视频文件
  if (fs.existsSync(videosDir)) {
    const videoFiles = fs.readdirSync(videosDir);
    for (const filename of videoFiles) {
      const filePath = path.join(videosDir, filename);
      const stats = fs.statSync(filePath);
      
      files.push({
        id: filename,
        originalName: filename,
        filename,
        path: filePath,
        size: stats.size,
        mimetype: 'video/*',
        uploadedAt: stats.birthtime
      });
    }
  }
  
  // 读取字幕文件
  if (fs.existsSync(subtitlesDir)) {
    const subtitleFiles = fs.readdirSync(subtitlesDir);
    for (const filename of subtitleFiles) {
      const filePath = path.join(subtitlesDir, filename);
      const stats = fs.statSync(filePath);
      
      files.push({
        id: filename,
        originalName: filename,
        filename,
        path: filePath,
        size: stats.size,
        mimetype: 'text/*',
        uploadedAt: stats.birthtime
      });
    }
  }

  const apiResponse: ApiResponse<UploadedFile[]> = {
    success: true,
    data: files,
    message: '获取文件列表成功'
  };

  res.json(apiResponse);
}));

/**
 * 删除上传的文件
 */
router.delete('/:filename', asyncHandler(async (req, res) => {
  const { filename } = req.params;
  
  const possiblePaths = [
    path.join(uploadDir, 'videos', filename),
    path.join(uploadDir, 'subtitles', filename)
  ];
  
  let deleted = false;
  for (const filePath of possiblePaths) {
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      deleted = true;
      break;
    }
  }
  
  if (!deleted) {
    throw createError('文件不存在', 404);
  }

  const apiResponse: ApiResponse = {
    success: true,
    message: '文件删除成功'
  };

  res.json(apiResponse);
}));

/**
 * 获取文件信息
 */
router.get('/:filename/info', asyncHandler(async (req, res) => {
  const { filename } = req.params;
  
  const possiblePaths = [
    { path: path.join(uploadDir, 'videos', filename), type: 'video' },
    { path: path.join(uploadDir, 'subtitles', filename), type: 'subtitle' }
  ];
  
  let fileInfo = null;
  for (const { path: filePath, type } of possiblePaths) {
    if (fs.existsSync(filePath)) {
      const stats = fs.statSync(filePath);
      fileInfo = {
        filename,
        type,
        size: stats.size,
        uploadedAt: stats.birthtime,
        modifiedAt: stats.mtime
      };
      break;
    }
  }
  
  if (!fileInfo) {
    throw createError('文件不存在', 404);
  }

  const apiResponse: ApiResponse = {
    success: true,
    data: fileInfo,
    message: '获取文件信息成功'
  };

  res.json(apiResponse);
}));

// 错误处理中间件
router.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  if (error instanceof multer.MulterError) {
    let message = '文件上传错误';
    
    switch (error.code) {
      case 'LIMIT_FILE_SIZE':
        message = '文件大小超过限制（最大500MB）';
        break;
      case 'LIMIT_FILE_COUNT':
        message = '文件数量超过限制';
        break;
      case 'LIMIT_UNEXPECTED_FILE':
        message = '意外的文件字段';
        break;
    }
    
    return res.status(400).json({
      success: false,
      error: message
    });
  }
  
  next(error);
});

export default router;
