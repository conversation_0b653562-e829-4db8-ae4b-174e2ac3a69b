import fs from 'fs';
import path from 'path';
import { SubtitleEntry, SubtitleFormat } from '../../../shared/types';

export class SubtitleParser {
  /**
   * 解析字幕文件
   */
  static async parseSubtitleFile(filePath: string): Promise<SubtitleEntry[]> {
    const content = fs.readFileSync(filePath, 'utf-8');
    const extension = path.extname(filePath).toLowerCase();
    
    switch (extension) {
      case '.srt':
        return this.parseSRT(content);
      case '.vtt':
        return this.parseVTT(content);
      case '.ass':
      case '.ssa':
        return this.parseASS(content);
      default:
        throw new Error(`不支持的字幕格式: ${extension}`);
    }
  }

  /**
   * 解析 SRT 格式字幕
   */
  private static parseSRT(content: string): SubtitleEntry[] {
    const entries: SubtitleEntry[] = [];
    const blocks = content.trim().split(/\n\s*\n/);

    for (const block of blocks) {
      const lines = block.trim().split('\n');
      if (lines.length < 3) continue;

      const index = parseInt(lines[0]);
      const timeMatch = lines[1].match(/(\d{2}):(\d{2}):(\d{2}),(\d{3})\s*-->\s*(\d{2}):(\d{2}):(\d{2}),(\d{3})/);
      
      if (!timeMatch) continue;

      const startTime = lines[1].split(' --> ')[0];
      const endTime = lines[1].split(' --> ')[1];
      const text = lines.slice(2).join('\n');

      const startMs = this.timeToMilliseconds(timeMatch[1], timeMatch[2], timeMatch[3], timeMatch[4]);
      const endMs = this.timeToMilliseconds(timeMatch[5], timeMatch[6], timeMatch[7], timeMatch[8]);

      entries.push({
        index,
        startTime,
        endTime,
        text: text.replace(/<[^>]*>/g, ''), // 移除HTML标签
        startMs,
        endMs
      });
    }

    return entries;
  }

  /**
   * 解析 VTT 格式字幕
   */
  private static parseVTT(content: string): SubtitleEntry[] {
    const entries: SubtitleEntry[] = [];
    const lines = content.split('\n');
    let index = 1;
    let i = 0;

    // 跳过 WEBVTT 头部
    while (i < lines.length && !lines[i].includes('-->')) {
      i++;
    }

    while (i < lines.length) {
      const line = lines[i].trim();
      
      if (line.includes('-->')) {
        const timeMatch = line.match(/(\d{2}):(\d{2}):(\d{2})\.(\d{3})\s*-->\s*(\d{2}):(\d{2}):(\d{2})\.(\d{3})/);
        
        if (timeMatch) {
          const startTime = `${timeMatch[1]}:${timeMatch[2]}:${timeMatch[3]},${timeMatch[4]}`;
          const endTime = `${timeMatch[5]}:${timeMatch[6]}:${timeMatch[7]},${timeMatch[8]}`;
          
          const startMs = this.timeToMilliseconds(timeMatch[1], timeMatch[2], timeMatch[3], timeMatch[4]);
          const endMs = this.timeToMilliseconds(timeMatch[5], timeMatch[6], timeMatch[7], timeMatch[8]);

          // 收集文本行
          const textLines: string[] = [];
          i++;
          while (i < lines.length && lines[i].trim() !== '') {
            textLines.push(lines[i].trim());
            i++;
          }

          entries.push({
            index,
            startTime,
            endTime,
            text: textLines.join('\n').replace(/<[^>]*>/g, ''),
            startMs,
            endMs
          });

          index++;
        }
      }
      i++;
    }

    return entries;
  }

  /**
   * 解析 ASS/SSA 格式字幕
   */
  private static parseASS(content: string): SubtitleEntry[] {
    const entries: SubtitleEntry[] = [];
    const lines = content.split('\n');
    let index = 1;

    for (const line of lines) {
      if (line.startsWith('Dialogue:')) {
        const parts = line.split(',');
        if (parts.length < 10) continue;

        const startTime = parts[1].trim();
        const endTime = parts[2].trim();
        const text = parts.slice(9).join(',').replace(/\\N/g, '\n').replace(/{[^}]*}/g, '');

        const startMs = this.assTimeToMilliseconds(startTime);
        const endMs = this.assTimeToMilliseconds(endTime);

        entries.push({
          index,
          startTime: this.assTimeToSRT(startTime),
          endTime: this.assTimeToSRT(endTime),
          text: text.trim(),
          startMs,
          endMs
        });

        index++;
      }
    }

    return entries;
  }

  /**
   * 时间转换为毫秒
   */
  private static timeToMilliseconds(hours: string, minutes: string, seconds: string, milliseconds: string): number {
    return parseInt(hours) * 3600000 + 
           parseInt(minutes) * 60000 + 
           parseInt(seconds) * 1000 + 
           parseInt(milliseconds);
  }

  /**
   * ASS时间格式转换为毫秒
   */
  private static assTimeToMilliseconds(time: string): number {
    const match = time.match(/(\d+):(\d{2}):(\d{2})\.(\d{2})/);
    if (!match) return 0;
    
    return parseInt(match[1]) * 3600000 + 
           parseInt(match[2]) * 60000 + 
           parseInt(match[3]) * 1000 + 
           parseInt(match[4]) * 10;
  }

  /**
   * ASS时间格式转换为SRT格式
   */
  private static assTimeToSRT(time: string): string {
    const match = time.match(/(\d+):(\d{2}):(\d{2})\.(\d{2})/);
    if (!match) return '00:00:00,000';
    
    return `${match[1].padStart(2, '0')}:${match[2]}:${match[3]},${match[4]}0`;
  }

  /**
   * 提取所有文本内容
   */
  static extractAllText(entries: SubtitleEntry[]): string {
    return entries.map(entry => entry.text).join(' ');
  }

  /**
   * 按时间范围过滤字幕
   */
  static filterByTimeRange(entries: SubtitleEntry[], startMs: number, endMs: number): SubtitleEntry[] {
    return entries.filter(entry => 
      entry.startMs >= startMs && entry.endMs <= endMs
    );
  }

  /**
   * 合并相邻的字幕条目
   */
  static mergeAdjacentEntries(entries: SubtitleEntry[], maxGapMs: number = 1000): SubtitleEntry[] {
    if (entries.length === 0) return [];

    const merged: SubtitleEntry[] = [];
    let current = { ...entries[0] };

    for (let i = 1; i < entries.length; i++) {
      const next = entries[i];
      
      // 如果间隔小于阈值，合并
      if (next.startMs - current.endMs <= maxGapMs) {
        current.endMs = next.endMs;
        current.endTime = next.endTime;
        current.text += ' ' + next.text;
      } else {
        merged.push(current);
        current = { ...next };
      }
    }
    
    merged.push(current);
    return merged;
  }

  /**
   * 验证字幕文件格式
   */
  static validateSubtitleFormat(filePath: string): SubtitleFormat | null {
    const extension = path.extname(filePath).toLowerCase();
    
    switch (extension) {
      case '.srt':
        return 'srt';
      case '.vtt':
        return 'vtt';
      case '.ass':
      case '.ssa':
        return 'ass';
      default:
        return null;
    }
  }
}
