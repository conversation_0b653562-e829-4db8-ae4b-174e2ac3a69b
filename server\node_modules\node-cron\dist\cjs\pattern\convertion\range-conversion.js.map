{"version": 3, "file": "range-conversion.js", "sourceRoot": "", "sources": ["../../../../src/pattern/convertion/range-conversion.ts"], "names": [], "mappings": ";;AAAA,kBAAe,CAAE,GAAG,EAAE;IAClB,SAAS,gBAAgB,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,OAAO;QAC1D,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC;QAC/B,MAAM,OAAO,GAAa,EAAE,CAAC;QAC7B,IAAI,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;QACzB,IAAI,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;QAE3B,IAAG,KAAK,GAAG,IAAI,EAAC,CAAC;YACb,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;YACtB,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;QAC1B,CAAC;QAED,KAAI,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC;YACtC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC;QAED,OAAO,UAAU,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;IACrE,CAAC;IAED,SAAS,YAAY,CAAC,UAAU;QAC5B,MAAM,UAAU,GAAG,uBAAuB,CAAC;QAC3C,IAAI,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACxC,OAAM,KAAK,KAAK,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAC,CAAC;YACtC,UAAU,GAAG,gBAAgB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC;YACzF,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACxC,CAAC;QACD,OAAO,UAAU,CAAC;IACtB,CAAC;IAED,SAAS,gBAAgB,CAAC,WAAW;QACjC,KAAI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAC,CAAC;YACxC,WAAW,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;QAClD,CAAC;QACD,OAAO,WAAW,CAAC;IACvB,CAAC;IAED,OAAO,gBAAgB,CAAC;AAC5B,CAAC,CAAC,EAAE,CAAC"}