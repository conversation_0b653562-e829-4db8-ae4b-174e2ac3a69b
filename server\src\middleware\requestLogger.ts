import { Request, Response, NextFunction } from 'express';

export const requestLogger = (req: Request, res: Response, next: NextFunction) => {
  const start = Date.now();
  
  // 记录请求开始
  console.log(`📥 ${req.method} ${req.path} - ${new Date().toISOString()}`);
  
  // 监听响应结束
  res.on('finish', () => {
    const duration = Date.now() - start;
    const statusColor = res.statusCode >= 400 ? '🔴' : '🟢';
    console.log(`📤 ${statusColor} ${req.method} ${req.path} - ${res.statusCode} - ${duration}ms`);
  });

  next();
};
