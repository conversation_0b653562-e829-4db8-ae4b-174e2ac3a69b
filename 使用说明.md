# 视频字幕处理系统 - 使用说明

## 🚀 快速开始

### 方法一：直接使用（推荐）
1. **双击运行** `start.bat` 文件
2. 系统会自动：
   - 检查 Node.js 环境
   - 安装必要依赖
   - 打开浏览器显示应用界面

### 方法二：手动启动
1. 打开命令行，进入项目目录
2. 运行：`npm run install:all` （安装依赖）
3. 直接打开 `index.html` 文件

## 📱 功能介绍

### 1. Gemini AI 助手
- **位置**：页面顶部黄色区域
- **功能**：测试 Gemini API 连接
- **使用**：
  1. 在文本框输入问题
  2. 点击"询问 Gemini"按钮
  3. 查看 AI 回答

### 2. 文件上传
- **支持格式**：
  - 视频：MP4, AVI, MOV, MKV, WebM
  - 字幕：SRT, VTT, ASS
- **操作**：
  1. 点击"选择视频文件"上传视频
  2. 点击"选择字幕文件"上传字幕（可多选）

### 3. 处理选项
- **视频模板**：选择输出视频的样式模板
- **关键词提取**：设置难度级别和数量
- **输出设置**：选择格式和质量

### 4. 处理流程
1. 上传文件 → 2. 设置选项 → 3. 开始处理 → 4. 下载结果

## 🔧 技术架构

### 前端
- **技术**：HTML5 + CSS3 + JavaScript
- **特性**：响应式设计，现代化UI
- **API集成**：Gemini AI API

### 后端（开发中）
- **技术**：Node.js + Express + TypeScript
- **功能**：文件处理、视频渲染、API服务

## 🌟 主要特性

### ✅ 已实现
- [x] 现代化用户界面
- [x] Gemini AI API 集成
- [x] 文件上传界面
- [x] 处理选项配置
- [x] 进度显示动画

### 🚧 开发中
- [ ] 后端API服务
- [ ] 字幕文件解析
- [ ] 关键词提取算法
- [ ] 视频处理引擎
- [ ] 批量处理功能

## 📋 环境要求

### 必需
- **浏览器**：Chrome, Firefox, Safari, Edge（现代浏览器）
- **网络**：需要访问 Gemini API

### 可选（完整功能）
- **Node.js**：18+ 版本
- **FFmpeg**：视频处理（后续需要）

## 🔑 API 配置

### Gemini API
- **API Key**：已内置在代码中
- **模型**：gemini-2.0-flash
- **用途**：AI问答、关键词分析

如需更换 API Key，请编辑 `index.html` 文件中的：
```javascript
const GEMINI_API_KEY = '您的新API密钥';
```

## 🎯 使用场景

### 教育领域
- 制作语言学习视频
- 生成词汇教学内容
- 创建发音指导材料

### 内容创作
- 视频字幕美化
- 关键信息提取
- 多语言内容处理

## 🛠️ 故障排除

### 常见问题

**Q: Gemini API 无法访问？**
A: 检查网络连接，确认 API Key 有效

**Q: 文件上传没有反应？**
A: 检查文件格式是否支持，文件大小是否过大

**Q: 处理进度卡住？**
A: 当前为演示模式，实际处理需要后端服务

### 技术支持
- 查看浏览器控制台错误信息
- 检查网络连接状态
- 确认文件格式正确

## 📞 联系方式

如有问题或建议，请联系开发团队。

---

**版本**：v1.0.0  
**更新时间**：2025-07-11  
**开发状态**：前端完成，后端开发中
