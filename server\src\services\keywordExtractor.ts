import { KeywordInfo, SubtitleEntry } from '../../../shared/types';

export class KeywordExtractor {
  private static readonly GEMINI_API_KEY = 'AIzaSyC6_sukxtXIiXx5-UkX1Dmu0bPQUfuW8C0';
  private static readonly GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';

  /**
   * 从字幕中提取关键词
   */
  static async extractKeywords(
    subtitles: SubtitleEntry[], 
    options: {
      maxKeywords: number;
      difficulty: 'all' | 'medium' | 'hard';
      language: 'en' | 'zh' | 'mixed';
    }
  ): Promise<KeywordInfo[]> {
    const text = subtitles.map(s => s.text).join(' ');
    
    // 使用Gemini API提取关键词
    const geminiKeywords = await this.extractKeywordsWithGemini(text, options);
    
    // 本地算法作为备选
    const localKeywords = this.extractKeywordsLocally(text, subtitles);
    
    // 合并和优化结果
    const combinedKeywords = this.combineKeywordResults(geminiKeywords, localKeywords);
    
    // 根据选项过滤和排序
    return this.filterAndSortKeywords(combinedKeywords, options);
  }

  /**
   * 使用Gemini API提取关键词
   */
  private static async extractKeywordsWithGemini(
    text: string, 
    options: { maxKeywords: number; difficulty: string; language: string }
  ): Promise<KeywordInfo[]> {
    try {
      const prompt = this.buildGeminiPrompt(text, options);
      
      const response = await fetch(this.GEMINI_API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-goog-api-key': this.GEMINI_API_KEY
        },
        body: JSON.stringify({
          contents: [{
            parts: [{ text: prompt }]
          }]
        })
      });

      const data = await response.json();
      
      if (data.candidates && data.candidates[0]) {
        const result = data.candidates[0].content.parts[0].text;
        return this.parseGeminiResponse(result);
      }
      
      throw new Error('Gemini API 响应格式错误');
    } catch (error) {
      console.error('Gemini API 错误:', error);
      return [];
    }
  }

  /**
   * 构建Gemini提示词
   */
  private static buildGeminiPrompt(text: string, options: any): string {
    const difficultyMap = {
      'all': '所有难度级别',
      'medium': '中等难度（适合中级学习者）',
      'hard': '高难度（适合高级学习者）'
    };

    return `
请从以下文本中提取关键词汇，用于制作教学视频：

文本内容：
${text.substring(0, 2000)}...

要求：
1. 提取 ${options.maxKeywords} 个最重要的关键词
2. 难度级别：${difficultyMap[options.difficulty]}
3. 包含中英文词汇
4. 为每个词提供：英文单词、中文翻译、音标、使用频率评分(1-10)

请按以下JSON格式返回：
[
  {
    "word": "example",
    "translation": "例子",
    "phonetic": "/ɪɡˈzæmpəl/",
    "frequency": 8,
    "difficulty": "medium"
  }
]

只返回JSON数组，不要其他解释。
`;
  }

  /**
   * 解析Gemini响应
   */
  private static parseGeminiResponse(response: string): KeywordInfo[] {
    try {
      // 提取JSON部分
      const jsonMatch = response.match(/\[[\s\S]*\]/);
      if (!jsonMatch) return [];

      const keywords = JSON.parse(jsonMatch[0]);
      
      return keywords.map((item: any) => ({
        word: item.word || '',
        translation: item.translation || '',
        pronunciation: item.phonetic || '',
        phonetic: item.phonetic || '',
        frequency: item.frequency || 1,
        difficulty: item.difficulty || 'medium',
        timestamps: []
      }));
    } catch (error) {
      console.error('解析Gemini响应失败:', error);
      return [];
    }
  }

  /**
   * 本地关键词提取算法
   */
  private static extractKeywordsLocally(text: string, subtitles: SubtitleEntry[]): KeywordInfo[] {
    const words = this.tokenizeText(text);
    const wordFreq = this.calculateWordFrequency(words);
    const keywords: KeywordInfo[] = [];

    // 过滤常见停用词
    const stopWords = new Set([
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
      'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did',
      'will', 'would', 'could', 'should', 'may', 'might', 'can', 'must',
      'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they',
      '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'
    ]);

    for (const [word, freq] of wordFreq.entries()) {
      if (word.length < 2 || stopWords.has(word.toLowerCase())) continue;
      
      // 查找词汇在字幕中的时间戳
      const timestamps = this.findWordTimestamps(word, subtitles);
      
      keywords.push({
        word,
        translation: '',
        pronunciation: '',
        phonetic: '',
        frequency: freq,
        timestamps,
        difficulty: this.estimateDifficulty(word, freq)
      });
    }

    return keywords.sort((a, b) => b.frequency - a.frequency);
  }

  /**
   * 文本分词
   */
  private static tokenizeText(text: string): string[] {
    // 处理中英文混合文本
    return text
      .replace(/[^\w\s\u4e00-\u9fff]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 0);
  }

  /**
   * 计算词频
   */
  private static calculateWordFrequency(words: string[]): Map<string, number> {
    const freq = new Map<string, number>();
    
    for (const word of words) {
      const normalizedWord = word.toLowerCase();
      freq.set(normalizedWord, (freq.get(normalizedWord) || 0) + 1);
    }
    
    return freq;
  }

  /**
   * 查找词汇在字幕中的时间戳
   */
  private static findWordTimestamps(word: string, subtitles: SubtitleEntry[]): number[] {
    const timestamps: number[] = [];
    const regex = new RegExp(`\\b${word}\\b`, 'gi');
    
    for (const subtitle of subtitles) {
      if (regex.test(subtitle.text)) {
        timestamps.push(subtitle.startMs);
      }
    }
    
    return timestamps;
  }

  /**
   * 估算词汇难度
   */
  private static estimateDifficulty(word: string, frequency: number): 'easy' | 'medium' | 'hard' {
    // 基于词长和频率的简单难度估算
    if (word.length <= 4 && frequency > 5) return 'easy';
    if (word.length <= 7 && frequency > 2) return 'medium';
    return 'hard';
  }

  /**
   * 合并关键词结果
   */
  private static combineKeywordResults(geminiKeywords: KeywordInfo[], localKeywords: KeywordInfo[]): KeywordInfo[] {
    const combined = new Map<string, KeywordInfo>();
    
    // 添加Gemini结果（优先级更高）
    for (const keyword of geminiKeywords) {
      combined.set(keyword.word.toLowerCase(), keyword);
    }
    
    // 补充本地结果
    for (const keyword of localKeywords) {
      const key = keyword.word.toLowerCase();
      if (!combined.has(key)) {
        combined.set(key, keyword);
      } else {
        // 合并时间戳信息
        const existing = combined.get(key)!;
        existing.timestamps = [...existing.timestamps, ...keyword.timestamps];
      }
    }
    
    return Array.from(combined.values());
  }

  /**
   * 过滤和排序关键词
   */
  private static filterAndSortKeywords(
    keywords: KeywordInfo[], 
    options: { maxKeywords: number; difficulty: string }
  ): KeywordInfo[] {
    let filtered = keywords;
    
    // 按难度过滤
    if (options.difficulty !== 'all') {
      filtered = keywords.filter(k => k.difficulty === options.difficulty);
    }
    
    // 按频率和重要性排序
    filtered.sort((a, b) => {
      const scoreA = a.frequency + (a.translation ? 2 : 0) + (a.phonetic ? 1 : 0);
      const scoreB = b.frequency + (b.translation ? 2 : 0) + (b.phonetic ? 1 : 0);
      return scoreB - scoreA;
    });
    
    // 限制数量
    return filtered.slice(0, options.maxKeywords);
  }

  /**
   * 生成发音标注
   */
  static async generatePronunciation(word: string): Promise<string> {
    try {
      const prompt = `请为英文单词 "${word}" 提供国际音标发音，只返回音标，格式如：/wɜːrd/`;
      
      const response = await fetch(this.GEMINI_API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-goog-api-key': this.GEMINI_API_KEY
        },
        body: JSON.stringify({
          contents: [{
            parts: [{ text: prompt }]
          }]
        })
      });

      const data = await response.json();
      
      if (data.candidates && data.candidates[0]) {
        const result = data.candidates[0].content.parts[0].text.trim();
        // 提取音标部分
        const phoneticMatch = result.match(/\/[^\/]+\//);
        return phoneticMatch ? phoneticMatch[0] : '';
      }
      
      return '';
    } catch (error) {
      console.error('生成发音标注失败:', error);
      return '';
    }
  }
}
