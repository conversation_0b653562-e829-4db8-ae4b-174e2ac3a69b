@echo off
echo 🚀 启动视频字幕处理系统...
echo.

REM 检查是否安装了 Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未检测到 Node.js
    echo 请先安装 Node.js: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js 已安装
echo.

REM 检查是否已安装依赖
if not exist "node_modules" (
    echo 📦 安装项目依赖...
    call npm install
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)

REM 检查服务器依赖
if not exist "server\node_modules" (
    echo 📦 安装服务器依赖...
    cd server
    call npm install
    if %errorlevel% neq 0 (
        echo ❌ 服务器依赖安装失败
        pause
        exit /b 1
    )
    cd ..
)

echo.
echo 🌐 打开浏览器访问应用...
start "" "index.html"

echo.
echo ✅ 系统已启动！
echo 📱 浏览器已自动打开 index.html
echo 🔧 如需启动后端服务，请运行: npm run dev
echo.
pause
