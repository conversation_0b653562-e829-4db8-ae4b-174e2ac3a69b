import express from 'express';
import cors from 'cors';
import path from 'path';
import { fileURLToPath } from 'url';
import uploadRoutes from './routes/upload';
import processRoutes from './routes/process';
import templateRoutes from './routes/templates';
import { errorHandler } from './middleware/errorHandler';
import { requestLogger } from './middleware/requestLogger';

const app = express();
const PORT = process.env.PORT || 3001;

// 中间件
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));
app.use(requestLogger);

// 静态文件服务
app.use('/uploads', express.static(path.join(__dirname, '../../uploads')));
app.use('/outputs', express.static(path.join(__dirname, '../../outputs')));
app.use('/templates', express.static(path.join(__dirname, '../../templates')));

// API 路由
app.use('/api/upload', uploadRoutes);
app.use('/api/process', processRoutes);
app.use('/api/templates', templateRoutes);

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// 错误处理中间件
app.use(errorHandler);

// 404 处理
app.use('*', (req, res) => {
  res.status(404).json({ 
    success: false, 
    error: 'API endpoint not found' 
  });
});

app.listen(PORT, () => {
  console.log(`🚀 服务器运行在 http://localhost:${PORT}`);
  console.log(`📁 上传目录: ${path.join(__dirname, '../../uploads')}`);
  console.log(`📤 输出目录: ${path.join(__dirname, '../../outputs')}`);
  console.log(`🎬 模板目录: ${path.join(__dirname, '../../templates')}`);
});

export default app;
