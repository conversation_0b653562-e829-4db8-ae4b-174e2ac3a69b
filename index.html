<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频字幕处理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .main-content {
            padding: 40px;
        }
        
        .upload-section {
            background: #f8f9ff;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 2px dashed #4facfe;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .upload-section:hover {
            border-color: #00f2fe;
            background: #f0f4ff;
        }
        
        .upload-area {
            margin-bottom: 20px;
        }
        
        .file-input {
            display: none;
        }
        
        .upload-btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            transition: transform 0.3s ease;
        }
        
        .upload-btn:hover {
            transform: translateY(-2px);
        }
        
        .file-info {
            margin-top: 15px;
            padding: 10px;
            background: white;
            border-radius: 10px;
            display: none;
        }
        
        .processing-section {
            background: #fff5f5;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .options-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .option-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .option-card h3 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #555;
        }
        
        .form-group select,
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
        }
        
        .form-group select:focus,
        .form-group input:focus {
            outline: none;
            border-color: #4facfe;
        }
        
        .process-btn {
            background: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 25px;
            font-size: 18px;
            cursor: pointer;
            width: 100%;
            transition: transform 0.3s ease;
        }
        
        .process-btn:hover {
            transform: translateY(-2px);
        }
        
        .process-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .progress-section {
            background: #f0fff4;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            display: none;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e1e5e9;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 15px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .status-text {
            text-align: center;
            font-size: 16px;
            color: #333;
        }
        
        .result-section {
            background: #f0f8ff;
            border-radius: 15px;
            padding: 30px;
            display: none;
        }
        
        .download-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: transform 0.3s ease;
        }
        
        .download-btn:hover {
            transform: translateY(-2px);
        }
        
        .gemini-section {
            background: #fff8e1;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .gemini-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #ffc107;
            border-radius: 10px;
            font-size: 16px;
            margin-bottom: 15px;
            resize: vertical;
            min-height: 100px;
        }
        
        .gemini-btn {
            background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        
        .gemini-btn:hover {
            transform: translateY(-2px);
        }
        
        .gemini-result {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-top: 15px;
            border-left: 4px solid #ffc107;
            display: none;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4facfe;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎬 视频字幕处理系统</h1>
            <p>上传视频和字幕，自动生成教学视频</p>
        </div>
        
        <div class="main-content">
            <!-- Gemini AI 测试区域 -->
            <div class="gemini-section">
                <h2>🤖 Gemini AI 助手</h2>
                <p>测试 Gemini API 连接，输入问题获取AI回答：</p>
                <textarea class="gemini-input" id="geminiInput" placeholder="请输入您的问题..."></textarea>
                <button class="gemini-btn" onclick="testGemini()">
                    <span id="geminiBtnText">询问 Gemini</span>
                </button>
                <div class="gemini-result" id="geminiResult"></div>
            </div>
            
            <!-- 文件上传区域 -->
            <div class="upload-section">
                <h2>📁 文件上传</h2>
                <div class="upload-area">
                    <input type="file" id="videoFile" class="file-input" accept="video/*">
                    <button class="upload-btn" onclick="document.getElementById('videoFile').click()">
                        选择视频文件
                    </button>
                    
                    <input type="file" id="subtitleFile" class="file-input" accept=".srt,.vtt,.ass" multiple>
                    <button class="upload-btn" onclick="document.getElementById('subtitleFile').click()">
                        选择字幕文件
                    </button>
                </div>
                <div class="file-info" id="fileInfo"></div>
            </div>
            
            <!-- 处理选项 -->
            <div class="processing-section">
                <h2>⚙️ 处理选项</h2>
                <div class="options-grid">
                    <div class="option-card">
                        <h3>视频模板</h3>
                        <div class="form-group">
                            <label>选择模板:</label>
                            <select id="templateSelect">
                                <option value="default">默认模板</option>
                                <option value="education">教育模板</option>
                                <option value="business">商务模板</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="option-card">
                        <h3>关键词提取</h3>
                        <div class="form-group">
                            <label>难度级别:</label>
                            <select id="difficultySelect">
                                <option value="all">全部</option>
                                <option value="medium">中等</option>
                                <option value="hard">困难</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>最大关键词数:</label>
                            <input type="number" id="maxKeywords" value="20" min="5" max="50">
                        </div>
                    </div>
                    
                    <div class="option-card">
                        <h3>输出设置</h3>
                        <div class="form-group">
                            <label>输出格式:</label>
                            <select id="formatSelect">
                                <option value="mp4">MP4</option>
                                <option value="webm">WebM</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>视频质量:</label>
                            <select id="qualitySelect">
                                <option value="medium">中等</option>
                                <option value="high">高清</option>
                                <option value="low">低质量</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <button class="process-btn" id="processBtn" onclick="startProcessing()">
                    🚀 开始处理
                </button>
            </div>
            
            <!-- 处理进度 -->
            <div class="progress-section" id="progressSection">
                <h2>📊 处理进度</h2>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="status-text" id="statusText">准备开始...</div>
            </div>
            
            <!-- 结果下载 -->
            <div class="result-section" id="resultSection">
                <h2>✅ 处理完成</h2>
                <p>您的教学视频已经生成完成！</p>
                <a href="#" class="download-btn" id="downloadBtn">📥 下载视频</a>
            </div>
        </div>
    </div>

    <script>
        const GEMINI_API_KEY = 'AIzaSyC6_sukxtXIiXx5-UkX1Dmu0bPQUfuW8C0';
        const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';
        
        // 测试 Gemini API
        async function testGemini() {
            const input = document.getElementById('geminiInput');
            const result = document.getElementById('geminiResult');
            const btnText = document.getElementById('geminiBtnText');
            
            if (!input.value.trim()) {
                alert('请输入问题！');
                return;
            }
            
            btnText.innerHTML = '<span class="loading"></span> 思考中...';
            result.style.display = 'none';
            
            try {
                const response = await fetch(GEMINI_API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-goog-api-key': GEMINI_API_KEY
                    },
                    body: JSON.stringify({
                        contents: [{
                            parts: [{
                                text: input.value
                            }]
                        }]
                    })
                });
                
                const data = await response.json();
                
                if (data.candidates && data.candidates[0]) {
                    result.innerHTML = `<strong>Gemini 回答:</strong><br>${data.candidates[0].content.parts[0].text}`;
                    result.style.display = 'block';
                } else {
                    throw new Error('API 返回格式错误');
                }
                
            } catch (error) {
                console.error('Gemini API 错误:', error);
                result.innerHTML = `<strong>错误:</strong> ${error.message}`;
                result.style.display = 'block';
            }
            
            btnText.textContent = '询问 Gemini';
        }
        
        // 文件选择处理
        document.getElementById('videoFile').addEventListener('change', updateFileInfo);
        document.getElementById('subtitleFile').addEventListener('change', updateFileInfo);
        
        function updateFileInfo() {
            const videoFile = document.getElementById('videoFile').files[0];
            const subtitleFiles = document.getElementById('subtitleFile').files;
            const fileInfo = document.getElementById('fileInfo');
            
            let info = '';
            if (videoFile) {
                info += `<p><strong>视频文件:</strong> ${videoFile.name} (${(videoFile.size / 1024 / 1024).toFixed(2)} MB)</p>`;
            }
            if (subtitleFiles.length > 0) {
                info += `<p><strong>字幕文件:</strong> `;
                for (let i = 0; i < subtitleFiles.length; i++) {
                    info += `${subtitleFiles[i].name}`;
                    if (i < subtitleFiles.length - 1) info += ', ';
                }
                info += `</p>`;
            }
            
            if (info) {
                fileInfo.innerHTML = info;
                fileInfo.style.display = 'block';
            } else {
                fileInfo.style.display = 'none';
            }
        }
        
        // 开始处理
        function startProcessing() {
            const videoFile = document.getElementById('videoFile').files[0];
            const subtitleFiles = document.getElementById('subtitleFile').files;
            
            if (!videoFile) {
                alert('请选择视频文件！');
                return;
            }
            
            if (subtitleFiles.length === 0) {
                alert('请选择字幕文件！');
                return;
            }
            
            // 显示进度区域
            document.getElementById('progressSection').style.display = 'block';
            document.getElementById('processBtn').disabled = true;
            document.getElementById('processBtn').textContent = '处理中...';
            
            // 模拟处理进度
            simulateProgress();
        }
        
        // 模拟处理进度
        function simulateProgress() {
            const progressFill = document.getElementById('progressFill');
            const statusText = document.getElementById('statusText');
            
            const steps = [
                { progress: 10, text: '上传文件中...' },
                { progress: 25, text: '解析字幕文件...' },
                { progress: 40, text: '提取关键词汇...' },
                { progress: 55, text: '生成发音标注...' },
                { progress: 70, text: '应用视频模板...' },
                { progress: 85, text: '渲染视频...' },
                { progress: 100, text: '处理完成！' }
            ];
            
            let currentStep = 0;
            
            const interval = setInterval(() => {
                if (currentStep < steps.length) {
                    const step = steps[currentStep];
                    progressFill.style.width = step.progress + '%';
                    statusText.textContent = step.text;
                    currentStep++;
                } else {
                    clearInterval(interval);
                    showResult();
                }
            }, 2000);
        }
        
        // 显示结果
        function showResult() {
            document.getElementById('resultSection').style.display = 'block';
            document.getElementById('processBtn').disabled = false;
            document.getElementById('processBtn').textContent = '🚀 开始处理';
            
            // 设置下载链接（这里是示例）
            document.getElementById('downloadBtn').href = '#';
            document.getElementById('downloadBtn').onclick = function() {
                alert('下载功能需要后端服务支持！');
                return false;
            };
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('视频字幕处理系统已加载');
            
            // 测试 Gemini API 连接
            document.getElementById('geminiInput').value = 'Hello, how are you?';
        });
    </script>
</body>
</html>
