{"name": "video-subtitle-server", "version": "1.0.0", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["video", "subtitle", "processing"], "author": "Your Name", "license": "MIT", "description": "视频字幕处理后端服务", "dependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/multer": "^2.0.0", "@types/node": "^24.0.13", "cors": "^2.8.5", "express": "^5.1.0", "multer": "^2.0.1", "node-cron": "^4.2.1", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3", "uuid": "^11.1.0"}}